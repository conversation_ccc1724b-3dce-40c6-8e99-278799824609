Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示优惠券列表
    visible: {
      type: Boolean,
      value: false
    },
    // 订单总金额，用于计算优惠券是否可用
    orderAmount: {
      type: Number,
      value: 0
    },
    // 已选择的优惠券ID
    selectedCouponId: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    loading: false,
    availableCoupons: [],
    expiredCoupons: [],
    usedCoupons: [],
    currentTab: 'available', // available, expired, used
    tabs: [
      { key: 'available', name: '可使用' },
      { key: 'expired', name: '已过期' },
      { key: 'used', name: '已使用' }
    ]
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 关闭弹窗
    onClose() {
      this.triggerEvent('close');
    },

    // 切换标签页
    onTabChange(e) {
      const tab = e.currentTarget.dataset.tab;
      this.setData({
        currentTab: tab
      });
    },

    // 选择优惠券
    onSelectCoupon(e) {
      const coupon = e.currentTarget.dataset.coupon;
      const couponId = coupon.coupon_usage_record.id;

      // 检查优惠券是否可用
      if (!this.isCouponAvailable(coupon)) {
        wx.showToast({
          title: '该优惠券不可用',
          icon: 'none'
        });
        return;
      }

      // 如果点击的是已选中的优惠券，则取消选择
      if (this.properties.selectedCouponId === couponId.toString()) {
        this.triggerEvent('select', { coupon: null });
      } else {
        this.triggerEvent('select', { coupon });
      }
    },

    // 加载优惠券列表
    loadCoupons() {
      if (this.data.loading) return;
      
      this.setData({ loading: true });
      
      const app = getApp();
      wx.request({
        url: `${app.globalData.baseUrl}/api/v1/wx/coupon/user-available-coupons`,
        method: 'GET',
        header: {
          'token': wx.getStorageSync('token')
        },
        success: (res) => {
          if (res.data.status === 200) {
            const data = res.data.data;

            // 预处理优惠券数据，添加格式化的折扣文本
            const processedAvailable = this.processCouponsData(data.available_coupons || []);
            const processedExpired = this.processCouponsData(data.expired_coupons || []);
            const processedUsed = this.processCouponsData(data.used_coupons || []);

            this.setData({
              availableCoupons: processedAvailable,
              expiredCoupons: processedExpired,
              usedCoupons: processedUsed
            });
          } else {
            wx.showToast({
              title: res.data.message || '加载失败',
              icon: 'none'
            });
          }
        },
        fail: (err) => {
          console.error('加载优惠券失败', err);
          wx.showToast({
            title: '网络错误',
            icon: 'none'
          });
        },
        complete: () => {
          this.setData({ loading: false });
        }
      });
    },

    // 处理优惠券数据，添加格式化的折扣文本
    processCouponsData(coupons) {
      return coupons.map(couponData => {
        const coupon = couponData.coupon;
        let discountText = '';

        if (coupon.type === 'PERCENTAGE') {
          // 计算折扣，例如 0.2 表示8折
          const discount = ((1 - coupon.quantity) * 10).toFixed(1);
          discountText = discount + '折';
        }

        return {
          ...couponData,
          discountText: discountText
        };
      });
    },

    // 检查优惠券是否可用
    isCouponAvailable(couponData) {
      const coupon = couponData.coupon;
      const orderAmount = this.properties.orderAmount;

      // 检查是否有不可用原因
      if (couponData.unavailable_reasons && couponData.unavailable_reasons.length > 0) {
        return false;
      }

      // 检查金额条件
      if (coupon.condition_amount > 0 && orderAmount < coupon.condition_amount) {
        return false;
      }

      return true;
    },

    // 计算优惠券折扣金额
    calculateDiscount(couponData, orderAmount) {
      const coupon = couponData.coupon;

      if (!this.isCouponAvailable(couponData)) {
        return 0;
      }

      if (coupon.type === 'DISCOUNT') {
        return Math.min(coupon.quantity, orderAmount);
      } else if (coupon.type === 'PERCENTAGE') {
        return orderAmount * coupon.quantity;
      } else if (coupon.type === 'FREE_SHIPPING') {
        // 免运费券的处理逻辑，这里假设运费为固定值
        return 0; // 实际应用中需要根据具体业务逻辑计算
      }

      return 0;
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 组件实例进入页面节点树时执行
    }
  },

  /**
   * 组件所在页面的生命周期
   */
  pageLifetimes: {
    show() {
      // 组件所在的页面被展示时执行
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'visible': function(visible) {
      if (visible) {
        this.loadCoupons();
      }
    }
  }
});
